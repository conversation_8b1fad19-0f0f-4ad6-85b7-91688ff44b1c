package com.tencent.yolo11ncnn

import android.content.res.AssetManager
import android.view.Surface
import com.haoxue.pose.PoseDataCallback

class YOLO11Ncnn {
    external fun loadModel(mgr: AssetManager?, modelid: Int, cpugpu: Int): Boolean
    external fun openCamera(facing: Int): Boolean
    external fun closeCamera(): Boolean
    external fun setOutputWindow(surface: Surface?): Boolean

    external fun registerPoseCallback(callback: PoseDataCallback?): Boolean

    external fun unregisterPoseCallback(): Boolean

    // 初始化并获取共享缓冲区（DirectByteBuffer）。maxPerson 为支持的最大人数。
    external fun initSharedBuffer(maxPerson: Int): java.nio.ByteBuffer

    /**
     * Kotlin 持有的共享关键点缓冲区，用于零拷贝读取 native 写入的数据。
     * 在 [initSharedBuffer] 调用后赋值。
     */
    @Volatile
    private var sharedBuffer: java.nio.ByteBuffer? = null

    fun createOrGetSharedBuffer(maxPerson: Int = 5): java.nio.ByteBuffer {
        var buf = sharedBuffer
        if (buf == null) {
            buf = initSharedBuffer(maxPerson)
            // 统一使用 native 字节序，防止字节序错乱
            buf.order(java.nio.ByteOrder.nativeOrder())
            sharedBuffer = buf
        }
        return buf
    }

    companion object {
        init {
            System.loadLibrary("yolo11ncnn")
        }
    }
}
