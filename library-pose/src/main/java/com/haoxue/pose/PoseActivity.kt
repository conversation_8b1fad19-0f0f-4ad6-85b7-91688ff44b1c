package com.haoxue.pose

import android.Manifest
import android.annotation.SuppressLint
import android.content.pm.PackageManager
import android.graphics.Color
import android.graphics.PixelFormat
import android.util.Log
import android.view.SurfaceHolder
import android.view.SurfaceView
import android.view.View
import android.view.WindowManager
import android.widget.AdapterView
import android.widget.AdapterView.OnItemSelectedListener
import android.widget.Button
import android.widget.LinearLayout
import android.widget.Spinner
import android.widget.TextView
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.haoxue.libcommon.gone
import com.haoxue.libcommon.router.RouterPaths
import com.haoxue.libcommon.ui.activity.BaseActivity
import com.haoxue.libcommon.visible
import com.haoxue.pose.databinding.MainBinding
import com.haoxue.pose.dialog.InitTipsDialog
import com.lazy.library.logging.Logcat
import com.tencent.yolo11ncnn.SportType
import com.tencent.yolo11ncnn.YOLO11Ncnn
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Route(path = RouterPaths.Pose.DETECTION)
class PoseActivity : BaseActivity<MainBinding>(R.layout.main), SurfaceHolder.Callback,
    PoseDataCallback {
    private val yolo11ncnn = YOLO11Ncnn()

    // 动作管理器：根据当前运动类型分发计数逻辑
    private lateinit var motionManager: MotionManager

    private var facing = 1

    private var spinnerModel: Spinner? = null
    private var spinnerCPUGPU: Spinner? = null
    private var current_model = 0
    private var current_cpugpu = 0

    private var cameraView: SurfaceView? = null

    private lateinit var numGo: TextView

    private var jumpCountersLayout: LinearLayout? = null

    private var isReady = false
    private var isReadyFinish = false
    private var readTips = false

    private var canCount = false
    private var wasCountingBefore = false // 用于跟踪之前的计数状态

    // 用于存储每个人的计数显示TextView
    private val personCountViews = mutableMapOf<Int, TextView>()


    override fun initCommonData() {
        window.addFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS)
        setContentView(R.layout.main)

        SmartAudioManager.initialize(this)
        SmartAudioManager.setOnPlayFinishListener { path ->
            Logcat.d("音频播放结束: $path")
            if (path == SmartAudioManager.getFullPath(SmartAudioManager.PoseAudio.WELCOME)) {
                readTips = true
            }
        }

        InitTipsDialog(this) {
            SmartAudioManager.play(SmartAudioManager.PoseAudio.WELCOME)
        }.show()

        numGo = findViewById<TextView>(R.id.numGo) as TextView
        cameraView = findViewById<View?>(R.id.cameraview) as SurfaceView

        jumpCountersLayout = findViewById<View?>(R.id.jumpCountersLayout) as LinearLayout

        cameraView!!.getHolder().setFormat(PixelFormat.RGBA_8888)
        cameraView!!.getHolder().addCallback(this)

        val buttonToggleDraw = findViewById<View?>(R.id.buttonToggleDraw) as Button
        // 设置初始状态 - 默认关闭绘制
        yolo11ncnn.setDrawEnabled(false)
        buttonToggleDraw.text = "开启绘制"

        buttonToggleDraw.setOnClickListener(object : View.OnClickListener {
            override fun onClick(arg0: View?) {
                val currentDrawEnabled = yolo11ncnn.isDrawEnabled()
                yolo11ncnn.setDrawEnabled(!currentDrawEnabled)
                buttonToggleDraw.text = if (!currentDrawEnabled) "关闭绘制" else "开启绘制"
                Logcat.d("绘制状态切换为: ${!currentDrawEnabled}")
            }
        })

        val buttonSwitchCamera = findViewById<View?>(R.id.buttonSwitchCamera) as Button
        buttonSwitchCamera.setOnClickListener(object : View.OnClickListener {
            override fun onClick(arg0: View?) {
                val new_facing = 1 - facing
                yolo11ncnn.closeCamera()
                yolo11ncnn.openCamera(new_facing)
                facing = new_facing
            }
        })

        val buttonResetCounter = findViewById<View?>(R.id.buttonResetCounter) as Button
        buttonResetCounter.setOnClickListener(object : View.OnClickListener {
            override fun onClick(arg0: View?) {
                jumpCountersLayout?.removeAllViews()
                personCountViews.clear()
                motionManager.resetCurrentCounter()
                SmartAudioManager.reset() // 重置智能音频管理器
                wasCountingBefore = false // 重置状态跟踪变量
            }
        })


        spinnerModel = findViewById<View?>(R.id.spinnerModel) as Spinner
        spinnerModel!!.setOnItemSelectedListener(object : OnItemSelectedListener {
            override fun onItemSelected(
                arg0: AdapterView<*>?,
                arg1: View?,
                position: Int,
                id: Long
            ) {
                if (position != current_model) {
                    current_model = position
                    reload()
                }
            }

            override fun onNothingSelected(arg0: AdapterView<*>?) {
            }
        })

        spinnerCPUGPU = findViewById<View?>(R.id.spinnerCPUGPU) as Spinner
        spinnerCPUGPU!!.setOnItemSelectedListener(object : OnItemSelectedListener {
            override fun onItemSelected(
                arg0: AdapterView<*>?,
                arg1: View?,
                position: Int,
                id: Long
            ) {
                if (position != current_cpugpu) {
                    current_cpugpu = position
                    reload()
                }
            }

            override fun onNothingSelected(arg0: AdapterView<*>?) {
            }
        })
        reload()

        // 读取外部参数：运动类型与支持人数
        val intentSport = intent?.getStringExtra("SPORT_TYPE")
        val sportType = intentSport?.let { runCatching { SportType.valueOf(it) }.getOrNull() }
            ?: SportType.JUMP_ROPE
        val maxPerson = intent?.getIntExtra("MAX_PERSON", 1) ?: 1

        // 初始化 MotionManager，添加姿态验证回调
        motionManager = MotionManager(
            { counts ->
                runOnUiThread {
                    if (!canCount || !readTips || !isReady || !isReadyFinish) return@runOnUiThread
                    updateJumpCountDisplay(counts)
                }
            },
            { isValid ->
                if (!readTips) return@MotionManager
                if (!isValid) {
                    canCount = false
                    SmartAudioManager.play(SmartAudioManager.PoseAudio.BEYOND_SCREEN)
                } else {
                    if (!isReady) {
                        isReady = true
                        SmartAudioManager.play(SmartAudioManager.PoseAudio.READY)
                        runOnUiThread {
                            // 实现倒计时动画 3,2,1,GO
                            lifecycleScope.launch {
                                delay(3000)
                                SmartAudioManager.play(SmartAudioManager.PoseAudio.START)
                                delay(1240)
                                numGo.visible()
                                numGo.text = "3"
                                delay(900)
                                numGo.text = "2"
                                delay(900)
                                numGo.text = "1"
                                delay(900)
                                numGo.text = "GO"
                                delay(1000)
                                numGo.gone()
                                isReadyFinish = true
                                canCount = true
                            }
                        }
                    } else {
                        canCount = true
                    }
                }
            },
            sportType,
            maxPerson
        )
    }

    override fun initCommonListener() {
    }

    override fun requestCommonData() {
    }

    private fun reload() {
        val ret_init = yolo11ncnn.loadModel(assets, current_model, current_cpugpu)
        if (!ret_init) {
            Log.e("MainActivity", "yolo11ncnn loadModel failed")
        }
    }


    // 实现PoseDataCallback接口的回调方法
    @SuppressLint("SetTextI18n")
    override fun onPoseDetected(
        personCount: Int,
        imageWidth: Int,
        imageHeight: Int
    ) {
        val floatBuffer = yolo11ncnn.createOrGetSharedBuffer().asFloatBuffer()
        val currentCanCount = canCount && readTips && isReady && isReadyFinish

        // 检测状态变化：从不可计数变为可计数
        if (!wasCountingBefore && currentCanCount) {
            // 状态刚刚变为可计数，重置计数器以清除累积的历史数据
            motionManager.resetCurrentCounter()
            Logcat.d("状态变为可计数，重置计数器清除累积数据")
        }
        wasCountingBefore = currentCanCount

        if (!currentCanCount) {
            // 不可计数状态下，仍然需要处理姿态验证，但不进行计数
            motionManager.onFrame(personCount, imageWidth, imageHeight, floatBuffer)
            return
        }

        // 将帧数据交给 MotionManager
        motionManager.onFrame(personCount, imageWidth, imageHeight, floatBuffer)
    }

    // 更新每个人的跳绳计数显示
    private fun updateJumpCountDisplay(jumpInfos: Map<Int, Int>) {
        // 智能音频播放处理（只处理第一个人）
        if (jumpInfos.isNotEmpty()) {
            val firstPersonCount = jumpInfos.values.first()
            SmartAudioManager.smartPlayCount(firstPersonCount)
        }

        // 清除旧的视图
        jumpCountersLayout?.removeAllViews()
        personCountViews.clear()

        // 为每个人创建一个计数显示
        for ((personId, info) in jumpInfos) {
            val textView = TextView(this)
            textView.text = "第${personId + 1}个人: ${info}次"
            textView.setTextColor(Color.WHITE)
            textView.textSize = 22f
            textView.setPadding(0, 4, 0, 4)
            jumpCountersLayout?.addView(textView)
            personCountViews[personId] = textView
        }
    }


    override fun surfaceChanged(holder: SurfaceHolder, format: Int, width: Int, height: Int) {
        yolo11ncnn.setOutputWindow(holder.getSurface())
    }

    override fun surfaceCreated(p0: SurfaceHolder) {

    }

    override fun surfaceDestroyed(p0: SurfaceHolder) {

    }

    public override fun onResume() {
        super.onResume()
        if (ContextCompat.checkSelfPermission(
                getApplicationContext(),
                Manifest.permission.CAMERA
            ) == PackageManager.PERMISSION_DENIED
        ) {
            ActivityCompat.requestPermissions(
                this,
                arrayOf<String>(Manifest.permission.CAMERA),
                REQUEST_CAMERA
            )
        }
        // 初始化共享缓冲区（假设最多 5 个人）
        yolo11ncnn.createOrGetSharedBuffer(5)

        yolo11ncnn.registerPoseCallback(this)
        yolo11ncnn.openCamera(facing)
    }

    public override fun onPause() {
        super.onPause()
        // 取消注册回调
        yolo11ncnn.unregisterPoseCallback()
        yolo11ncnn.closeCamera()
    }

    override fun onDestroy() {
        super.onDestroy()
        SmartAudioManager.release()
    }

    companion object {
        const val REQUEST_CAMERA: Int = 100
    }
}
