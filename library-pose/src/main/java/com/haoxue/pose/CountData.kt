package com.haoxue.pose
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

enum class MovementState {
    IDLE,      // 静止状态
    JUMPING    // 跳绳状态
}

class CountData(
    private val windowSize: Int = 6,                 // 平滑窗口大小	增大可提高稳定性，减小可提高响应速度
    private val minRelativeAmplitude: Float = 0.15f,  // 最小相对振幅，根据用户群体调整，儿童可适当降低
    private val minCycleDuration: Long = 100,         // 最小周期持续时间(ms)
    private val maxCycleDuration: Long = 5000,        // 最大周期持续时间(ms)
    private val confidenceThreshold: Float = 0.5f,    // 关键点置信度阈值
    private val noiseThresholdMultiplier: Float = 2.5f, // 噪声阈值倍数
    private val staticDurationThreshold: Long = 3000, // 静态持续时间阈值(ms)
    private val peakQualityThreshold: Float = 0.3f,   // 峰值质量阈值	提高可减少误检，降低可提高检出率
) {
    private val hipYHistory = arrayListOf<Float>()     // 髋部y坐标历史
    private val leftAnkleYHistory = arrayListOf<Float>()  // 左踝y坐标历史
    private val rightAnkleYHistory = arrayListOf<Float>() // 右踝y坐标历史
    private val timestampHistory = arrayListOf<Long>() // 时间戳历史
    private var jumpCount = 0
    private var lastJumpTime: Long = 0
    private val peakTimes = arrayListOf<Long>()     // 峰值时间点

    // 自适应阈值参数
    private var adaptiveMinAmplitude: Float = 0f      // 自适应最小振幅
    private var zhendHeightData: Float = 10f      // 自适应最小振幅
    private var dataRange: Float = 0f                 // 当前数据范围
    private var baselineNoise: Float = 0f             // 基础噪声水平

    // 状态机参数
    private var state = MovementState.IDLE
    private var consecutiveJumps = 0                  // 连续有效跳跃次数
    private var stateStartTime: Long = 0              // 当前状态开始时间
    private var lastSignificantMovementTime: Long = 0 // 上次显著运动时间
    private var isStatic = false                      // 是否处于静态状态

    /**
     * 更新关键点数据并检测跳绳
     */
    fun update(
        keypoints: FloatArray,
        timestamp: Long = System.currentTimeMillis(),
        callback: (num: Int) -> Unit
    ) {
        // 提取关键身体部位点
        val bodyKeyPoints = extractBodyKeyPoints(keypoints) ?: return

        // 添加到历史记录
        hipYHistory.add(bodyKeyPoints[1])  // 髋部Y坐标
        leftAnkleYHistory.add(bodyKeyPoints[2])  // 左踝Y坐标
        rightAnkleYHistory.add(bodyKeyPoints[3]) // 右踝Y坐标
        timestampHistory.add(timestamp)

        // 保持历史数据在合理范围内
        if (hipYHistory.size > windowSize * 5) {
            hipYHistory.removeAt(0)
            leftAnkleYHistory.removeAt(0)
            rightAnkleYHistory.removeAt(0)
            timestampHistory.removeAt(0)
        }

        // 更新自适应阈值
        updateAdaptiveThreshold()

        // 检测跳绳
        callback(detectJump())
    }

    /**
     * 提取关键身体部位点用于跳绳检测
     * 返回: [hipCenterX, hipCenterY, leftAnkleY, rightAnkleY, leftKneeY, rightKneeY]
     */
    private fun extractBodyKeyPoints(keypoints: FloatArray): FloatArray? {
        if (keypoints.size < 17 * 3) return null

        // COCO 17关键点索引
        // 11: 左髋, 12: 右髋, 13: 左膝, 14: 右膝, 15: 左踝, 16: 右踝
        val nose = extractKeyPoint(keypoints, 0)
        val leftHip = extractKeyPoint(keypoints, 11)
        val rightHip = extractKeyPoint(keypoints, 12)
        val leftKnee = extractKeyPoint(keypoints, 13)
        val rightKnee = extractKeyPoint(keypoints, 14)
        val leftAnkle = extractKeyPoint(keypoints, 15)
        val rightAnkle = extractKeyPoint(keypoints, 16)

        // 验证关键点置信度
        if (nose[2] < confidenceThreshold
            || leftHip[2] < confidenceThreshold
            || rightHip[2] < confidenceThreshold
            || leftAnkle[2] < confidenceThreshold
            || rightAnkle[2] < confidenceThreshold
        ) {
            return null
        }

        // 计算髋部中心点
        val hipCenterX = (leftHip[0] + rightHip[0]) / 2f
        val hipCenterY = (leftHip[1] + rightHip[1]) / 2f

        return floatArrayOf(
            hipCenterX, hipCenterY,
            leftAnkle[1], rightAnkle[1],
            leftKnee[1], rightKnee[1]
        )
    }

    private fun extractKeyPoint(keypoints: FloatArray, index: Int): FloatArray {
        return floatArrayOf(
            keypoints[index * 3],     // x
            keypoints[index * 3 + 1], // y
            keypoints[index * 3 + 2]  // confidence
        )
    }

    /**
     * 检测是否为真正的跳绳动作（双脚离地）
     * 通过分析双脚同步性和运动模式来区分跳绳和蹲起
     */
    private fun isValidJumpRopeMovement(peakIndex: Int): Boolean {
        if (leftAnkleYHistory.size <= peakIndex || rightAnkleYHistory.size <= peakIndex) {
            return false
        }
        val feetSyncScore = checkFeetOffGround(peakIndex)
        return feetSyncScore
    }

    /**
     * 检测双脚是否同时离地
     * 核心思路：分析双脚Y坐标的变化趋势和速度
     */
    private fun checkFeetOffGround(peakIndex: Int): Boolean {
        val windowStart = max(1, peakIndex - windowSize / 2)
        val windowEnd = min(leftAnkleYHistory.size - 1, peakIndex + windowSize / 2)

        if (windowEnd <= windowStart + 1) return false

        // 1. 检查双脚运动方向一致性
        val directionConsistency = checkMovementDirectionConsistency(windowStart, windowEnd)

        // 2. 检查是否有向上运动（离地特征）
        val hasUpwardMovement = checkUpwardMovement(windowStart, windowEnd)

        return directionConsistency && hasUpwardMovement
    }


    /**
     * 检查双脚运动方向一致性
     */
    private fun checkMovementDirectionConsistency(start: Int, end: Int): Boolean {
        var consistentCount = 0
        var totalCount = 0

        for (i in start until end) {
            val leftDelta = leftAnkleYHistory[i] - leftAnkleYHistory[i - 1]
            val rightDelta = rightAnkleYHistory[i] - rightAnkleYHistory[i - 1]

            // 检查运动方向是否一致（同时向上或同时向下）
            val sameDirection = (leftDelta * rightDelta) >= 0

            if (sameDirection) {
                consistentCount++
            }
            totalCount++
        }

        // 方向一致性要达到80%以上
        return totalCount > 0 && (consistentCount.toFloat() / totalCount) >= 0.8f
    }


    /**
     * 检查是否有向上运动（离地特征）
     */
    private fun checkUpwardMovement(start: Int, end: Int): Boolean {
        // 计算双脚的平均Y坐标变化
        val leftStartY = leftAnkleYHistory[start]
        val leftEndY = leftAnkleYHistory[end - 1]
        val rightStartY = rightAnkleYHistory[start]
        val rightEndY = rightAnkleYHistory[end - 1]

        val leftMovement = leftStartY - leftEndY  // Y坐标减小表示向上
        val rightMovement = rightStartY - rightEndY

        // 双脚都有向上运动，且运动幅度足够
        val minUpwardMovement = 5f  // 最小向上运动像素
        return abs(leftMovement) >= minUpwardMovement && abs(rightMovement) >= minUpwardMovement
    }


    /**
     * 更新自适应阈值
     */
    private fun updateAdaptiveThreshold() {
        if (hipYHistory.size < windowSize * 3) return

        // 计算历史数据的范围
        val recentData = hipYHistory.takeLast(windowSize * 2)
        val maxY = recentData.maxOrNull() ?: 0f
        val minY = recentData.minOrNull() ?: 0f
        dataRange = maxY - minY

        // 计算短期波动作为噪声基准
        val shortTermData = hipYHistory.takeLast(windowSize)
        val shortTermDiff = mutableListOf<Float>()

        for (i in 1 until shortTermData.size) {
            shortTermDiff.add(abs(shortTermData[i] - shortTermData[i - 1]))
        }

        // 使用中位数作为噪声基准（比平均值更鲁棒）
        baselineNoise = shortTermDiff.sorted()[shortTermDiff.size / 2]

        // 设置自适应最小振幅（至少是噪声基准的数倍，或数据范围的比例）
        adaptiveMinAmplitude =
            max(dataRange * minRelativeAmplitude, baselineNoise * noiseThresholdMultiplier)
    }

    /**
     * 检测跳绳动作
     */
    private fun detectJump(): Int {
        if (hipYHistory.size < windowSize * 2) return jumpCount

        val currentTime = timestampHistory.last()

        // 检查是否处于静态状态
        if (currentTime - lastSignificantMovementTime > staticDurationThreshold) {
            isStatic = true
        }

        // 数据平滑处理
        val smoothedY = smooth(hipYHistory)

        // 检测波峰和波谷
        val peaks = findPeaks(smoothedY)
        val valleys = findValleys(smoothedY)


        if (peaks.isNotEmpty() && valleys.isNotEmpty()) {
            if (smoothedY[peaks.last()] - smoothedY[valleys.last()] < zhendHeightData) {
                return jumpCount
            }
        }

        // 如果有新的波峰，检查是否构成有效跳绳
        if (peaks.isNotEmpty() && (peakTimes.isEmpty() || timestampHistory[peaks.last()] > (peakTimes.lastOrNull()
                ?: 0))
        ) {

            val currentPeakIndex = peaks.last()
            val currentPeakTime = timestampHistory[currentPeakIndex]

            // 查找对应的前一个波谷
            val prevValleyIndex =
                valleys.filter { it < currentPeakIndex }.maxOrNull() ?: return jumpCount

            // 计算相对振幅
            val amplitude = smoothedY[currentPeakIndex] - smoothedY[prevValleyIndex]

            // 计算周期时间(毫秒)
            val cycleDuration = currentPeakTime - timestampHistory[prevValleyIndex]

            // 判断是否为显著运动
            val isSignificantMovement = amplitude >= adaptiveMinAmplitude

            // 新增：检查是否为有效跳绳动作
            val isValidJumpRope = if (isSignificantMovement) {
                isValidJumpRopeMovement(currentPeakIndex)
            } else {
                false
            }

            if (isSignificantMovement && isValidJumpRope) {
                lastSignificantMovementTime = currentPeakTime
                isStatic = false
            }

            // 静态状态下不处理
            if (isStatic) return jumpCount

            // 状态机逻辑 - 现在只有通过跳绳验证的动作才会被处理
            when (state) {
                MovementState.IDLE -> {
                    if (isSignificantMovement && isValidJumpRope) {
                        consecutiveJumps = 1
                        state = MovementState.JUMPING
                        stateStartTime = currentPeakTime
                        println("检测到疑似跳绳动作，进入计数状态")
                    }
                }

                MovementState.JUMPING -> {
                    if (isSignificantMovement && isValidJumpRope &&
                        cycleDuration in minCycleDuration..maxCycleDuration &&
                        currentPeakTime - lastJumpTime > minCycleDuration
                    ) {

                        jumpCount++
                        peakTimes.add(currentPeakTime)
                        lastJumpTime = currentPeakTime
                        println("检测到第 $jumpCount 次有效跳绳！")
                    } else if (currentTime - lastJumpTime > maxCycleDuration * 2) {
                        // 长时间没有有效跳跃，回到过渡状态
                        state = MovementState.IDLE
                        stateStartTime = currentTime
                        consecutiveJumps = 0
                        println("长时间无有效跳绳，回到过渡状态")
                    }
                }
            }
        }
        return jumpCount
    }

    /**
     * 平滑数据(移动平均)
     */
    private fun smooth(data: List<Float>): FloatArray {
        if (data.size < windowSize) return data.toFloatArray()

        val result = FloatArray(data.size - windowSize + 1)
        for (i in result.indices) {
            result[i] = data.subList(i, i + windowSize).average().toFloat()
        }
        return result
    }

    /**
     * 检测波峰
     */
    private fun findPeaks(data: FloatArray): List<Int> {
        val peaks = mutableListOf<Int>()
        for (i in 1 until data.size - 1) {
            // 局部最大值，且突出度满足要求
            if (data[i] > data[i - 1] && data[i] > data[i + 1] &&
                data[i] - max(data[i - 1], data[i + 1]) > 0
            ) {

                // 评估峰值质量
                val quality = evaluatePeakQuality(data, i)
                if (quality >= peakQualityThreshold) {
                    peaks.add(i)
                }
            }
        }
        return peaks
    }

    /**
     * 检测波谷
     */
    private fun findValleys(data: FloatArray): List<Int> {
        val valleys = mutableListOf<Int>()
        for (i in 1 until data.size - 1) {
            // 局部最小值，且突出度满足要求
            if (data[i] < data[i - 1] && data[i] < data[i + 1] &&
                min(data[i - 1], data[i + 1]) - data[i] > 0
            ) {
                valleys.add(i)
            }
        }
        return valleys
    }

    /**
     * 评估峰值质量
     */
    private fun evaluatePeakQuality(data: FloatArray, peakIndex: Int): Float {
        // 找到左右两侧的谷值
        val leftValleyIndex = findLeftValley(data, peakIndex)
        val rightValleyIndex = findRightValley(data, peakIndex)

        if (leftValleyIndex < 0 || rightValleyIndex < 0) return 0f

        // 计算上升和下降阶段的对称性
        val riseDuration = peakIndex - leftValleyIndex
        val fallDuration = rightValleyIndex - peakIndex
        val symmetry =
            1.0f - abs(riseDuration - fallDuration).toFloat() / max(riseDuration, fallDuration)

        // 计算峰值尖锐度（与周围点的差异）
        val sharpness =
            (data[peakIndex] - data[peakIndex - 1]) * (data[peakIndex] - data[peakIndex + 1]) /
                    (adaptiveMinAmplitude * adaptiveMinAmplitude)

        // 综合质量评分
        return (symmetry * 0.6f + sharpness * 0.4f).coerceIn(0f, 1f)
    }

    private fun findLeftValley(data: FloatArray, peakIndex: Int): Int {
        var valleyIndex = peakIndex - 1
        while (valleyIndex > 0 && data[valleyIndex] > data[valleyIndex - 1]) {
            valleyIndex--
        }
        return if (valleyIndex > 0) valleyIndex else -1
    }

    private fun findRightValley(data: FloatArray, peakIndex: Int): Int {
        var valleyIndex = peakIndex + 1
        val n = data.size
        while (valleyIndex < n - 1 && data[valleyIndex] > data[valleyIndex + 1]) {
            valleyIndex++
        }
        return if (valleyIndex < n - 1) valleyIndex else -1
    }

    fun reset() {
        // 清除所有历史数据
        hipYHistory.clear()
        leftAnkleYHistory.clear()
        rightAnkleYHistory.clear()
        timestampHistory.clear()
        peakTimes.clear()

        // 重置计数和时间
        jumpCount = 0
        lastJumpTime = 0

        // 重置自适应参数
        adaptiveMinAmplitude = 0f
        zhendHeightData = 10f
        dataRange = 0f
        baselineNoise = 0f

        // 重置状态机
        state = MovementState.IDLE
        consecutiveJumps = 0
        stateStartTime = 0
        lastSignificantMovementTime = 0
        isStatic = false
    }
}