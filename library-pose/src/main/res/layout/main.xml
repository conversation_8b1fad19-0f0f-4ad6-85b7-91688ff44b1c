<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:visibility="gone">

            <Spinner
                android:id="@+id/spinnerModel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawSelectorOnTop="true"
                android:entries="@array/model_array" />

            <Spinner
                android:id="@+id/spinnerCPUGPU"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawSelectorOnTop="true"
                android:entries="@array/cpugpu_array" />

        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <SurfaceView
                android:id="@+id/cameraview"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:padding="30dp"
                android:scaleType="fitXY"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/camrea_bg" />

            <ImageView
                android:paddingTop="100dp"
                android:paddingBottom="100dp"
                android:paddingLeft="30dp"
                android:paddingRight="30dp"
                android:scaleType="fitXY"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/camera_user" />

            <TextView
                android:gravity="center"
                android:background="#80000000"
                android:visibility="gone"
                android:id="@+id/numGo"
                android:textColor="@color/common_color_white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                android:layout_width="0dp"
                android:textSize="140dp"
                android:textStyle="bold"
                android:text="GO"
                android:layout_height="0dp"/>

            <LinearLayout
                android:id="@+id/jumpCountersLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:orientation="vertical"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent">

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="right"
                app:layout_constraintBottom_toBottomOf="parent">

                <Button
                    android:layout_marginRight="10dp"
                    android:id="@+id/buttonToggleDraw"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="关闭绘制" />

                <Button
                    android:layout_marginRight="10dp"
                    android:id="@+id/buttonSwitchCamera"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="切换摄像头" />

                <Button
                    android:id="@+id/buttonResetCounter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="重置检测"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent" />
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>
</layout>
